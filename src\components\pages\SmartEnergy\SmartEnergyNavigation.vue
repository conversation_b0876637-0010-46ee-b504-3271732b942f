<template>
    <div>
        <div class="nav-all-wrapper" v-if="showFlag === 3">
            <!-- 导航按钮 -->
            <div class="nav-all-btns">
                <div
                    v-for="item in navigationItems"
                    :key="item.id"
                    :class="active === item.id ? 'active-btn' : 'btn'"
                    @click="setPage(item.id)"
                >
                    {{ item.label }}
                </div>
            </div>
        </div>
        <energy-consumption-dialog
            :part="part"
            :pickId="energyConsumptionId"
            v-if="energyConsumptionDialogShow"
            @closeDialog="closeDialog"
        />
    </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, getCurrentInstance } from "vue";
import EnergyConsumptionDialog from "./subgroup/EnergyConsumptionDialog.vue";
import {
    energy_map_c,
    energy_map_electricity,
    energy_map_steam,
    energy_map_gas,
    energy_map_water,
} from "../../../assets/js/api/smartEnergy";

// 常量定义
const ENERGY_TYPES = {
    CARBON: 1,
    ELECTRICITY: 2,
    WATER: 3,
    GAS: 4,
    STEAM: 5,
};

const HEAT_MAP_CONFIG = {
    radius: 55,
    opacity: [0, 0.8],
};

// 导航项配置
const navigationItems = [
    { id: ENERGY_TYPES.CARBON, label: "碳排放" },
    { id: ENERGY_TYPES.ELECTRICITY, label: "用电" },
    { id: ENERGY_TYPES.WATER, label: "水" },
    { id: ENERGY_TYPES.GAS, label: "天然气" },
    { id: ENERGY_TYPES.STEAM, label: "蒸汽量" },
];

// 响应式数据
const { proxy } = getCurrentInstance();
const energyConsumptionDialogShow = ref(false);
const energyConsumptionId = ref(null);
const showFlag = ref(1);
const active = ref(0);
const part = ref(1);

// 地图相关数据
let points = [];
let max = 0;
let heatmap = null;
let polyline1 = null;
// 园区边界坐标
const parkBoundaryPath = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 7.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
// API映射配置
const energyApiMap = {
    [ENERGY_TYPES.CARBON]: { api: energy_map_c, dataKey: 'result' },
    [ENERGY_TYPES.ELECTRICITY]: { api: energy_map_electricity, dataKey: 'number' },
    [ENERGY_TYPES.WATER]: { api: energy_map_water, dataKey: 'number' },
    [ENERGY_TYPES.GAS]: { api: energy_map_gas, dataKey: 'number' },
    [ENERGY_TYPES.STEAM]: { api: energy_map_steam, dataKey: 'number' },
};

// 清除地图数据
const clearMapData = () => {
    window.map1?.clearMap();
    if (window.heatmap) {
        window.heatmap.setMap(null);
    }
    points = [];
    energyConsumptionId.value = null;
};

// 处理API响应数据
const processApiData = (data, dataKey) => {
    return data.map(item => ({
        id: item.id,
        lat: item.latitude,
        lng: item.longitude,
        count: item[dataKey],
    }));
};

// 获取能源数据
const fetchEnergyData = async (type) => {
    try {
        const config = energyApiMap[type];
        if (!config) return;

        const res = await config.api();
        if (res.data.success && res.data.data?.length) {
            points = processApiData(res.data.data, config.dataKey);
            max = points[0]?.count || 0;

            if (window.toggle === 3) {
                initHeatMap();
            }
        }
    } catch (error) {
        console.error('获取能源数据失败:', error);
    }
};

// 设置页面
const setPage = (type) => {
    if (active.value === type) {
        // 取消选择
        active.value = 0;
        clearMapData();
        initBoundary();
    } else {
        // 选择新类型
        active.value = type;
        part.value = type;
        initBoundary();
        fetchEnergyData(type);
    }
};

// 初始化热力图
const initHeatMap = () => {
    window.map1?.clearMap();
    if (window.heatmap) {
        window.heatmap.setMap(null);
    }
    initBoundary();

    window.heatmap = new AMap.HeatMap(window.map1, HEAT_MAP_CONFIG);
    window.heatmap.setDataSet({
        data: points,
        max: max,
    });

    // 创建可点击的圆形标记
    points.forEach(point => {
        const circleMarker = new AMap.CircleMarker({
            center: [point.lng, point.lat],
            radius: HEAT_MAP_CONFIG.radius,
            strokeColor: "",
            strokeWeight: 2,
            strokeOpacity: 0.5,
            fillColor: "rgba(18, 48, 77, 0.1)",
            fillOpacity: 0,
            zIndex: 10,
            bubble: true,
            cursor: "pointer",
            clickable: true,
            exData: point,
        });
        circleMarker.setMap(window.map1);
        circleMarker.on("click", markerClick);
    });

    window.map1?.setFitView();
};

// 标记点击事件处理
const markerClick = (e) => {
    const exData = e.target._opts?.exData;
    if (exData?.id) {
        energyConsumptionId.value = exData.id;
        energyConsumptionDialogShow.value = true;
        proxy.$loading.show();
    }
};

// 初始化园区边界
const initBoundary = () => {
    if (window.map1 && window.toggle === 3) {
        polyline1 = new AMap.Polyline({
            path: parkBoundaryPath,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
onMounted(() => {
    showFlag.value = window.toggle;
    proxy.$bus.on("change_toggle", (val) => {
        console.log("change_toggle ", val);
        showFlag.value = val;
        active.value = 0;
    });

    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
    initBoundary();
});
onUnmounted(() => {
    proxy.$bus.off("change_toggle");
});

const closeDialog = (value) => {
    console.log(value);
    if (value == "energyConsumption") {
        energyConsumptionDialogShow.value = false;
    }
    proxy.$loading.hide();
};
</script>

<style lang="less" scoped>
.nav-all-wrapper {
    height: 862px;
    position: absolute;
    z-index: 1000;
    top: 96px;
    left: 496px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    pointer-events: none;

    .nav-all-btns {
        display: flex;
        flex-direction: column;
        gap: 24px;
        pointer-events: auto;
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px;
        text-align: center;

        .btn {
            width: 130px;
            height: 32px;
            background: url("../../../assets/images/jump-btn/self-nav.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
        }

        .active-btn {
            width: 130px;
            height: 32px;
            background: url("../../../assets/images/jump-btn/self-nav-active.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
        }
    }
}
</style>
